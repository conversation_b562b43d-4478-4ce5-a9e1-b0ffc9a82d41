{"name": "quickload", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/datetimepicker": "^7.6.3", "@react-native-community/geolocation": "^3.2.1", "@react-native-firebase/app": "^19.2.2", "@react-native-firebase/messaging": "^19.2.2", "@react-navigation/drawer": "^6.6.14", "@react-navigation/material-top-tabs": "^6.6.13", "@react-navigation/native": "^6.1.16", "@react-navigation/native-stack": "^6.9.25", "@stripe/stripe-react-native": "^0.37.3", "axios": "^1.6.8", "prop-types": "^15.8.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-android-location-enabler": "^2.0.1", "react-native-date-picker": "^5.0.0", "react-native-document-picker": "^9.1.1", "react-native-element-dropdown": "^2.10.2", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.15.0", "react-native-html-to-pdf": "^0.12.0", "react-native-network-logger": "^1.15.0", "react-native-otp-entry": "^1.6.0", "react-native-pager-view": "^6.3.0", "react-native-paper": "^5.12.3", "react-native-permissions": "^4.1.5", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.8.1", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "^3.34.0", "react-native-share": "^10.2.1", "react-native-tab-view": "^3.5.2", "react-native-timeline-flatlist": "^0.8.0", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}